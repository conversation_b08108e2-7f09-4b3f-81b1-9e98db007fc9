# 爱宠记APP系统架构设计文档
**架构师**: David  
**版本**: v1.0  
**创建日期**: 2025-08-14  

---

## 1. 架构概览

### 1.1 系统架构图

```mermaid
graph TB
    subgraph "客户端层"
        A[React Native App<br/>iOS/Android/Web]
        B[微信小程序<br/>轻量级访问]
    end
    
    subgraph "API网关层"
        C[API Gateway<br/>Kong/AWS API Gateway]
        D[负载均衡<br/>Nginx/ALB]
    end
    
    subgraph "微服务层"
        E[用户服务<br/>Node.js/Express]
        F[设备服务<br/>Node.js/Express]
        G[定位服务<br/>Node.js/Express]
        H[AI服务<br/>Python/FastAPI]
        I[消息服务<br/>Node.js/Socket.io]
    end
    
    subgraph "数据存储层"
        J[(主数据库<br/>PostgreSQL)]
        K[(缓存层<br/>Redis)]
        L[(时序数据<br/>InfluxDB)]
        M[(文件存储<br/>AWS S3/阿里云OSS)]
    end
    
    subgraph "消息队列"
        N[Apache Kafka<br/>实时数据流]
        O[RabbitMQ<br/>业务消息]
    end
    
    subgraph "外部服务"
        P[地图服务<br/>高德/百度API]
        Q[短信服务<br/>阿里云短信]
        R[推送服务<br/>Firebase/APNs)]
        S[AI模型服务<br/>OpenAI/本地部署)]
    end
    
    subgraph "IoT设备"
        T[智能项圈<br/>STM32+4G/NB-IoT]
    end
    
    A --> C
    B --> C
    C --> D
    D --> E & F & G & H & I
    E --> J & K
    F --> J & K & N
    G --> L & K
    H --> S & K
    I --> O & R
    T --> N
    N --> G & F
```

### 1.2 技术栈选择

| 层级 | 技术选择 | 理由 |
|------|----------|------|
| **前端** | React Native + TypeScript | 95%代码共享，成熟生态，性能接近原生 |
| **后端** | Node.js + Express | JavaScript全栈，高并发处理，丰富中间件 |
| **数据库** | PostgreSQL + Redis | ACID事务 + 高性能缓存，成熟稳定 |
| **实时通信** | Socket.io + WebSocket | 实时位置推送，低延迟通信 |
| **消息队列** | Apache Kafka | 高吞吐量，实时数据流处理 |
| **AI服务** | Python + FastAPI | 专业AI库支持，高性能API |
| **容器化** | Docker + Kubernetes | 弹性伸缩，高可用部署 |
| **云服务** | AWS/阿里云混合 | 全球覆盖，成本优化 |

---

## 2. 跨平台移动架构

### 2.1 React Native架构设计

#### 2.1.1 项目结构
```
aichong-app/
├── src/
│   ├── components/           # 通用组件
│   │   ├── atoms/           # 原子组件
│   │   ├── molecules/       # 分子组件
│   │   └── organisms/       # 有机体组件
│   ├── screens/             # 页面组件
│   │   ├── Home/
│   │   ├── Map/
│   │   ├── Device/
│   │   └── AI/
│   ├── navigation/          # 导航配置
│   ├── services/            # API服务
│   ├── store/               # 状态管理
│   ├── hooks/               # 自定义Hooks
│   ├── utils/               # 工具函数
│   └── types/               # TypeScript类型
├── ios/                     # iOS原生代码
├── android/                 # Android原生代码
└── scripts/                 # 构建脚本
```

#### 2.1.2 状态管理架构
```typescript
// Redux Toolkit + RTK Query
├── store/
│   ├── index.ts            # Store配置
│   ├── api/                # RTK Query API
│   │   ├── deviceApi.ts
│   │   ├── locationApi.ts
│   │   └── userApi.ts
│   └── slices/             # Redux Slices
│       ├── deviceSlice.ts
│       ├── locationSlice.ts
│       └── userSlice.ts
```

#### 2.1.3 关键功能模块
```typescript
// 定位服务
interface LocationService {
  getCurrentLocation(): Promise<Location>;
  startTracking(interval: number): void;
  stopTracking(): void;
  setGeofence(config: GeofenceConfig): Promise<void>;
}

// 设备通信
interface DeviceService {
  connectDevice(deviceId: string): Promise<boolean>;
  sendCommand(command: DeviceCommand): Promise<void>;
  startVoiceCall(): Promise<void>;
  receiveData(callback: (data: DeviceData) => void): void;
}

// AI对话
interface AIService {
  sendMessage(message: string): Promise<AIResponse>;
  getPetEmotion(): Promise<EmotionData>;
  startConversation(mode: 'text' | 'voice'): void;
}
```

### 2.2 原生模块集成

#### 2.2.1 iOS原生模块
```swift
// 蓝牙通信模块
import CoreBluetooth

class BluetoothManager: NSObject, CBCentralManagerDelegate {
    func connectToDevice(_ deviceId: String) -> Bool
    func sendData(_ data: Data) -> Bool
    func startScanning()
}

// 推送通知
import UserNotifications

class PushNotificationManager {
    func registerForPushNotifications()
    func handleNotification(_ notification: UNNotification)
}
```

#### 2.2.2 Android原生模块
```kotlin
// 蓝牙通信
import android.bluetooth.BluetoothAdapter

class BluetoothModule {
    fun connectToDevice(deviceId: String): Boolean
    fun sendData(data: ByteArray): Boolean
    fun startDiscovery()
}

// 后台服务
import android.app.Service

class LocationService : Service() {
    fun startForegroundTracking()
    fun stopTracking()
}
```

---

## 3. 后端微服务架构

### 3.1 微服务划分

#### 3.1.1 用户服务 (User Service)
```typescript
// 用户管理、认证授权
POST   /api/v1/auth/register
POST   /api/v1/auth/login
POST   /api/v1/auth/logout
GET    /api/v1/users/profile
PUT    /api/v1/users/profile
POST   /api/v1/users/avatar/upload
```

#### 3.1.2 设备服务 (Device Service)
```typescript
// 设备管理、固件更新
POST   /api/v1/devices/register
GET    /api/v1/devices/:deviceId
PUT    /api/v1/devices/:deviceId
POST   /api/v1/devices/:deviceId/firmware/update
GET    /api/v1/devices/:deviceId/status
```

#### 3.1.3 定位服务 (Location Service)
```typescript
// 实时定位、轨迹记录
GET    /api/v1/location/:deviceId/current
GET    /api/v1/location/:deviceId/history
POST   /api/v1/location/:deviceId/geofence
WebSocket /ws/location/:deviceId
```

#### 3.1.4 AI服务 (AI Service)
```typescript
// 人宠对话、情绪分析
POST   /api/v1/ai/chat
POST   /api/v1/ai/emotion/analyze
GET    /api/v1/ai/conversations/:conversationId
POST   /api/v1/ai/voice/convert
```

### 3.2 API设计规范

#### 3.2.1 RESTful API标准
```typescript
// 统一的响应格式
interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
  timestamp: string;
}

// 分页查询
interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
}

// 错误响应
interface ErrorResponse {
  code: string;
  message: string;
  details?: any;
}
```

#### 3.2.2 WebSocket实时通信
```typescript
// 客户端连接
const socket = io('wss://api.aichong.com', {
  query: { deviceId: 'device_123' },
  auth: { token: 'jwt_token' }
});

// 实时位置推送
socket.on('location_update', (data: LocationUpdate) => {
  console.log('New location:', data);
});

// 设备状态变化
socket.on('device_status', (status: DeviceStatus) => {
  console.log('Device status:', status);
});

// 紧急警报
socket.on('emergency_alert', (alert: EmergencyAlert) => {
  console.log('Emergency:', alert);
});
```

---

## 4. 数据库设计

### 4.1 数据库架构

#### 4.1.1 主数据库 (PostgreSQL)
```sql
-- 用户表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20) UNIQUE,
    nickname VARCHAR(50),
    avatar_url TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 宠物表
CREATE TABLE pets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    name VARCHAR(50) NOT NULL,
    breed VARCHAR(100),
    age INTEGER,
    weight DECIMAL(5,2),
    avatar_url TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 设备表
CREATE TABLE devices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    device_code VARCHAR(50) UNIQUE NOT NULL,
    pet_id UUID REFERENCES pets(id),
    name VARCHAR(50),
    model VARCHAR(50),
    firmware_version VARCHAR(20),
    battery_level INTEGER CHECK (battery_level >= 0 AND battery_level <= 100),
    status VARCHAR(20) DEFAULT 'online',
    last_seen TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 位置历史表
CREATE TABLE locations (
    id BIGSERIAL PRIMARY KEY,
    device_id UUID REFERENCES devices(id),
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    accuracy DECIMAL(5, 2),
    altitude DECIMAL(8, 2),
    speed DECIMAL(5, 2),
    direction DECIMAL(5, 2),
    battery_level INTEGER,
    recorded_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 电子围栏表
CREATE TABLE geofences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    pet_id UUID REFERENCES pets(id),
    name VARCHAR(100),
    center_latitude DECIMAL(10, 8),
    center_longitude DECIMAL(11, 8),
    radius_meters INTEGER,
    shape_type VARCHAR(20) DEFAULT 'circle',
    polygon_coordinates JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW()
);

-- AI对话记录表
CREATE TABLE ai_conversations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    pet_id UUID REFERENCES pets(id),
    user_message TEXT,
    ai_response TEXT,
    emotion_detected VARCHAR(50),
    created_at TIMESTAMP DEFAULT NOW()
);
```

#### 4.1.2 时序数据库 (InfluxDB)
```javascript
// 位置数据模式
{
  measurement: "device_locations",
  tags: {
    device_id: "device_123",
    pet_id: "pet_456",
    user_id: "user_789"
  },
  fields: {
    latitude: 39.9042,
    longitude: 116.4074,
    accuracy: 3.2,
    altitude: 43.5,
    speed: 0.8,
    direction: 135.6,
    battery_level: 78
  },
  timestamp: new Date()
}
```

#### 4.1.3 缓存设计 (Redis)
```javascript
// 用户会话缓存
key: `user:session:${userId}`
value: {
  userId: "uuid",
  deviceIds: ["device_123", "device_456"],
  lastActivity: "timestamp"
}
ttl: 7200 // 2小时

// 设备实时状态
key: `device:status:${deviceId}`
value: {
  online: true,
  batteryLevel: 78,
  lastLocation: { lat: 39.9042, lng: 116.4074 },
  lastUpdate: "timestamp"
}
ttl: 300 // 5分钟

// 地理围栏缓存
key: `geofence:active:${petId}`
value: {
  fences: [...geofenceData],
  lastCheck: "timestamp"
}
ttl: 600 // 10分钟
```

### 4.2 数据分片策略

#### 4.2.1 按地理位置分片
```sql
-- 按省份分片
CREATE TABLE locations_beijing (
    CHECK (latitude BETWEEN 39.4 AND 41.1)
    -- 继承主表结构
) INHERITS (locations);

CREATE TABLE locations_shanghai (
    CHECK (latitude BETWEEN 30.7 AND 31.9)
) INHERITS (locations);
```

#### 4.2.2 按时间分片
```sql
-- 按月创建分区表
CREATE TABLE locations_2025_08 PARTITION OF locations
    FOR VALUES FROM ('2025-08-01') TO ('2025-09-01');
```

---

## 5. 实时通信架构

### 5.1 MQTT协议用于设备通信

#### 5.1.1 主题设计
```
aichong/{userId}/{deviceId}/location
aichong/{userId}/{deviceId}/status
aichong/{userId}/{deviceId}/command
aichong/{userId}/{deviceId}/alert
aichong/{userId}/{deviceId}/voice
```

#### 5.1.2 消息格式
```json
// 位置上报
{
  "type": "location_update",
  "device_id": "device_123",
  "timestamp": "2025-08-14T10:30:00Z",
  "data": {
    "latitude": 39.9042,
    "longitude": 116.4074,
    "accuracy": 3.2,
    "battery": 78
  }
}

// 设备命令
{
  "type": "device_command",
  "command": "start_ringing",
  "parameters": {
    "volume": 80,
    "duration": 60
  }
}
```

### 5.2 WebSocket用于APP实时更新

#### 5.2.1 房间管理
```typescript
// 用户房间
io.to(`user_${userId}`).emit('location_update', data);

// 设备房间
io.to(`device_${deviceId}`).emit('status_change', status);

// 地理围栏房间
io.to(`geofence_${petId}`).emit('fence_alert', alert);
```

---

## 6. AI服务架构

### 6.1 AI模型部署

#### 6.1.1 模型架构
```python
# FastAPI AI服务
from fastapi import FastAPI
import torch
from transformers import pipeline

app = FastAPI()

# 情绪识别模型
emotion_classifier = pipeline(
    "text-classification",
    model="pet-emotion-model-v1"
)

# 对话生成模型
chat_generator = pipeline(
    "text-generation",
    model="pet-chat-model-v1"
)

@app.post("/api/v1/ai/emotion/analyze")
async def analyze_emotion(text: str):
    result = emotion_classifier(text)
    return {"emotion": result[0]["label"], "confidence": result[0]["score"]}

@app.post("/api/v1/ai/chat")
async def generate_chat(user_input: str, pet_context: dict):
    prompt = f"Pet context: {pet_context}\nUser: {user_input}\nAI:"
    response = chat_generator(prompt, max_length=100)
    return {"response": response[0]["generated_text"]}
```

#### 6.2 模型优化策略

##### 6.2.1 模型缓存
```python
import redis
from functools import lru_cache

redis_client = redis.Redis(host='localhost', port=6379)

@lru_cache(maxsize=1000)
def get_cached_response(key: str):
    return redis_client.get(key)

def cache_response(key: str, response: dict, ttl: int = 3600):
    redis_client.setex(key, ttl, json.dumps(response))
```

##### 6.2.2 异步处理
```python
from celery import Celery

celery_app = Celery('ai_tasks', broker='redis://localhost:6379')

@celery_app.task
def process_ai_chat_async(user_input: str, pet_id: str):
    # 耗时AI处理
    result = chat_generator(user_input)
    # 存储结果
    save_chat_result(pet_id, result)
    return result
```

---

## 7. 安全架构

### 7.1 认证授权

#### 7.1.1 JWT Token设计
```typescript
interface JwtPayload {
  userId: string;
  email: string;
  deviceIds: string[];
  permissions: string[];
  iat: number;
  exp: number;
}

// Token刷新机制
const refreshToken = async (refreshToken: string) => {
  const response = await api.post('/auth/refresh', { refreshToken });
  return response.data;
};
```

#### 7.1.2 API权限控制
```typescript
// 基于角色的权限控制
const permissions = {
  'user': ['read:own_data'],
  'admin': ['read:all_data', 'write:all_data'],
  'device': ['read:device_data', 'write:device_data']
};
```

### 7.2 数据加密

#### 7.2.1 传输加密
```typescript
// HTTPS强制
app.use((req, res, next) => {
  if (req.header('x-forwarded-proto') !== 'https') {
    res.redirect(`https://${req.header('host')}${req.url}`);
  } else {
    next();
  }
});
```

#### 7.2.2 存储加密
```typescript
// 敏感数据加密
import crypto from 'crypto';

const encrypt = (text: string, key: string): string => {
  const cipher = crypto.createCipher('aes-256-cbc', key);
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return encrypted;
};
```

---

## 8. 性能优化

### 8.1 缓存策略

#### 8.1.1 多层缓存架构
```
用户层 -> CDN -> API缓存 -> 应用缓存 -> 数据库
```

#### 8.1.2 缓存更新策略
```typescript
// 写穿模式
await redis.setex(`location:${deviceId}`, 300, JSON.stringify(location));
await db.locations.create(location);

// 缓存失效
const invalidateCache = async (keys: string[]) => {
  await Promise.all(keys.map(key => redis.del(key)));
};
```

### 8.2 数据库优化

#### 8.2.1 查询优化
```sql
-- 位置查询优化
CREATE INDEX idx_locations_device_time ON locations(device_id, recorded_at DESC);

-- 地理查询优化
CREATE INDEX idx_locations_geo ON locations USING GIST (
  ST_MakePoint(longitude, latitude)
);
```

#### 8.2.2 连接池配置
```typescript
// PostgreSQL连接池
import { Pool } from 'pg';

const pool = new Pool({
  host: process.env.DB_HOST,
  port: 5432,
  database: process.env.DB_NAME,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});
```

---

## 9. 部署架构

### 9.1 容器化部署

#### 9.1.1 Docker配置
```dockerfile
# Node.js服务Dockerfile
FROM node:18-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
EXPOSE 3000

CMD ["node", "dist/server.js"]
```

#### 9.1.2 Kubernetes配置
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: location-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: location-service
  template:
    metadata:
      labels:
        app: location-service
    spec:
      containers:
      - name: location-service
        image: aichong/location-service:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
```

### 9.2 监控告警

#### 9.2.1 应用监控
```typescript
// Prometheus指标
const promClient = require('prom-client');

const httpRequestDuration = new promClient.Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status_code']
});

app.use((req, res, next) => {
  const start = Date.now();
  res.on('finish', () => {
    httpRequestDuration
      .labels(req.method, req.route?.path || 'unknown', res.statusCode)
      .observe((Date.now() - start) / 1000);
  });
  next();
});
```

#### 9.2.2 业务监控
```typescript
// 核心业务指标
const businessMetrics = {
  activeDevices: new promClient.Gauge({
    name: 'active_devices_total',
    help: 'Total number of active devices'
  }),
  locationUpdates: new promClient.Counter({
    name: 'location_updates_total',
    help: 'Total number of location updates'
  }),
  geofenceAlerts: new promClient.Counter({
    name: 'geofence_alerts_total',
    help: 'Total number of geofence alerts'
  })
};
```

---

## 10. 扩展性设计

### 10.1 水平扩展

#### 10.1.1 微服务拆分策略
```typescript
// 服务发现
import { Consul } from 'consul';

const consul = new Consul({ host: 'localhost', port: 8500 });

const registerService = async (serviceName: string, serviceId: string, port: number) => {
  await consul.agent.service.register({
    name: serviceName,
    id: serviceId,
    address: 'localhost',
    port,
    check: {
      http: `http://localhost:${port}/health`,
      interval: '30s'
    }
  });
};
```

#### 10.1.2 数据库分片
```typescript
// 基于用户ID的分片
const getShardKey = (userId: string): string => {
  const hash = crypto.createHash('md5').update(userId).digest('hex');
  const shardIndex = parseInt(hash.substr(0, 8), 16) % 4;
  return `shard_${shardIndex}`;
};
```

### 10.2 多租户支持

#### 10.2.1 租户隔离策略
```typescript
// 基于Schema的隔离
const getTenantSchema = (tenantId: string): string => {
  return `tenant_${tenantId}`;
};

// 基于行的隔离
const addTenantFilter = (query: any, tenantId: string) => {
  return query.where('tenant_id', tenantId);
};
```