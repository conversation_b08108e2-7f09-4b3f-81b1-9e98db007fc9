# 爱宠记APP高保真原型设计规范
**设计负责人**: Alex  
**原型版本**: v2.0 (高保真)  
**创建日期**: 2025-08-14  

---

## 1. 高保真原型设计框架

### 1.1 原型保真度等级定义

| 维度 | 低保真 | 中保真 | **高保真** (当前) | 最终产品 |
|------|--------|--------|-------------------|----------|
| **视觉细化** | 线框图 | 基础样式 | **像素级精确** | 实际实现 |
| **功能广度** | 核心功能 | 主要功能 | **100%功能覆盖** | 全功能 |
| **功能深度** | 基础交互 | 完整流程 | **真实数据+复杂交互** | 实际逻辑 |
| **交互性** | 点击跳转 | 基础动效 | **真实手势+物理反馈** | 原生交互 |
| **数据模型** | 占位符 | 模拟数据 | **真实API数据** | 实时数据 |

### 1.2 原型目标与范围

#### 1.2.1 核心测试场景
1. **新用户首次使用** - 5分钟完整引导流程
2. **日常监控模式** - 实时位置查看+状态管理
3. **紧急寻宠模式** - 一键报警+导航寻宠
4. **AI对话体验** - 多轮对话+情绪识别
5. **设备管理流程** - 配对+设置+故障处理

#### 1.2.2 原型交付标准
- ✅ **100%视觉还原** - 与设计稿像素级一致
- ✅ **真实交互体验** - 支持所有手势操作
- ✅ **动态数据模拟** - 真实API响应模拟
- ✅ **多状态覆盖** - 正常/异常/边界状态
- ✅ **跨设备适配** - iOS/Android/Web响应式

---

## 2. Figma高保真原型设计系统

### 2.1 设计系统升级（高保真版）

#### 2.1.1 精确色彩系统
```
/* 主品牌色 - 精确值 */
--color-primary: #FF6B35;        /* 温暖橙 - 主操作 */
--color-primary-light: #FF8A65;  /* 悬停状态 */
--color-primary-dark: #E55100;   /* 按下状态 */

/* 状态色 - 精确语义 */
--color-success: #00C853;        /* 成功状态 */
--color-warning: #FFA726;        /* 警告状态 */
--color-error: #F44336;          /* 错误状态 */
--color-info: #29B6F6;           /* 信息提示 */

/* 中性色 - 完整色阶 */
--color-gray-50: #FAFAFA;
--color-gray-100: #F5F5F5;
--color-gray-200: #EEEEEE;
--color-gray-300: #E0E0E0;
--color-gray-400: #BDBDBD;
--color-gray-500: #9E9E9E;
--color-gray-600: #757575;
--color-gray-700: #616161;
--color-gray-800: #424242;
--color-gray-900: #212121;
```

#### 2.1.2 精确间距系统
```css
/* 8点网格系统 - 精确值 */
--spacing-xs: 4px;
--spacing-sm: 8px;
--spacing-md: 16px;
--spacing-lg: 24px;
--spacing-xl: 32px;
--spacing-2xl: 48px;
--spacing-3xl: 64px;

/* 特殊间距 - 精确语义 */
--spacing-safe-area: 20px;      /* iPhone安全区域 */
--spacing-thumb-zone: 72px;     /* 拇指操作区域 */
--spacing-nav-bar: 88px;        /* 导航栏高度 */
```

### 2.2 组件库完整定义

#### 2.2.1 原子级组件（精确规格）

**主按钮组件 - 完整状态**
```
┌─────────────────────────────────┐
│  默认状态                          │
│  背景: #FF6B35                   │
│  文字: #FFFFFF                   │
│  高度: 48px                      │
│  圆角: 24px                      │
│  字体: 16px Medium               │
│  阴影: 0 2px 8px rgba(0,0,0,0.1) │
└─────────────────────────────────┘

┌─────────────────────────────────┐
│  悬停状态                          │
│  背景: #FF8A65                   │
│  阴影: 0 4px 16px rgba(0,0,0,0.2)│
│  变换: translateY(-1px)          │
└─────────────────────────────────┘

┌─────────────────────────────────┐
│  按下状态                          │
│  背景: #E55100                   │
│  阴影: 0 1px 4px rgba(0,0,0,0.2) │
│  变换: translateY(1px)           │
└─────────────────────────────────┘
```

**宠物状态卡片 - 完整交互**
```typescript
// 组件状态定义
interface PetCardState {
  online: boolean;
  batteryLevel: number;
  signalStrength: number;
  lastUpdate: Date;
  isCharging: boolean;
}

// 交互状态机
const cardStates = {
  default: { scale: 1, shadow: '0 2px 8px rgba(0,0,0,0.1)' },
  hover: { scale: 1.02, shadow: '0 4px 16px rgba(0,0,0,0.15)' },
  pressed: { scale: 0.98, shadow: '0 1px 4px rgba(0,0,0,0.2)' },
  loading: { opacity: 0.7, pulse: true }
};
```

#### 2.2.2 业务组件完整定义

**实时位置卡片 - 高保真版**
```
┌─────────────────────────────────────────┐
│  [旺财头像 - 48px圆角]                   │
│  旺财 · 2岁 · 金毛寻回犬                │
│                                         │
│  📍 北京市朝阳区三里屯                  │
│  距离您当前位置 156米                   │
│                                         │
│  状态指示器                             │
│  ● 在线  🔋 78%  📶 4G                │
│                                         │
│  ┌─────────────────────────────────────┐│
│  │  [实时位置按钮 - 主色填充]            ││
│  │  [导航按钮 - 描边样式]                ││
│  │  [语音通话 - 图标按钮]                ││
│  └─────────────────────────────────────┘│
└─────────────────────────────────────────┘
```

---

## 3. 完整原型页面设计

### 3.1 启动引导流程 (5屏完整流程)

#### 3.1.1 欢迎页 (Splash Screen)
```
┌─────────────────────────────────┐
│                                 │
│     [动态Logo动画]               │
│                                 │
│    爱宠记                       │
│    AI智能宠物守护                │
│                                 │
│    [进度条 - 3秒加载动画]         │
│                                 │
│    v2.0.1  隐私政策 用户协议      │
└─────────────────────────────────┘

交互说明:
- Logo: 呼吸动画效果
- 进度条: 平滑填充动画
- 背景: 渐变橙色到淡黄
- 总时长: 3秒自动跳转
```

#### 3.1.2 权限请求页
```
┌─────────────────────────────────┐
│  [返回按钮]                       │
│                                 │
│     [权限图标动画]               │
│                                 │
│  我们需要以下权限                │
│                                 │
│  📍 位置权限                    │
│  获取精准位置，守护宠物安全      │
│  [了解详情]                     │
│                                 │
│  📱 通知权限                    │
│  及时接收宠物状态提醒            │
│  [了解详情]                     │
│                                 │
│  🎤 麦克风权限                  │
│  实现人宠语音对话功能            │
│  [了解详情]                     │
│                                 │
│  [允许全部权限] [稍后设置]        │
└─────────────────────────────────┘
```

#### 3.1.3 设备配对页
```
┌─────────────────────────────────┐
│  [步骤指示器: ● ● ○]              │
│                                 │
│  配对您的智能项圈                 │
│                                 │
│  [设备图片 - 360°旋转展示]         │
│                                 │
│  1. 打开项圈电源开关              │
│  2. 确保手机蓝牙已开启            │
│  3. 将项圈靠近手机                │
│                                 │
│  [扫描设备按钮]                   │
│  [手动输入设备码]                 │
│                                 │
│  [设备列表 - 可滚动]               │
│  └─ 爱宠记-ABC123 (信号强度)      │
│  └─ 爱宠记-DEF456 (信号强度)      │
└─────────────────────────────────┘
```

### 3.2 主界面完整状态

#### 3.2.1 首页 - 正常状态
```
┌─────────────────────────────────────────┐
│  状态栏 [9:41 AM] 📶 🔋 78%              │
├─────────────────────────────────────────┤
│  [搜索框] [通知铃铛🔔] [设置⚙️]          │
├─────────────────────────────────────────┤
│  我的宠物                                │
│                                         │
│  ┌─────────────────────────────────────┐│
│  │ [旺财头像]                          ││
│  │ 旺财 · 在线                        ││
│  │ ● 绿色圆点  78%电量                ││
│  │                                     ││
│  │ 今日活动: 5230步 · 3.2公里          ││
│  │ 📍 在家附近 156米                   ││
│  └─────────────────────────────────────┘│
│                                         │
│  快速操作                                │
│  ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐       │
│  │实时  │ │导航  │ │语音  │ │围栏  │       │
│  │定位  │ │寻宠  │ │通话  │ │设置  │       │
│  └─────┘ └─────┘ └─────┘ └─────┘       │
│                                         │
│  最近活动                                │
│  ┌─────────────────────────────────────┐│
│  │ 10:30 - 旺财离开了安全区域           ││
│  │ 09:15 - 电量低于20%提醒             ││
│  └─────────────────────────────────────┘│
└─────────────────────────────────────────┘
```

#### 3.2.2 首页 - 紧急状态
```
┌─────────────────────────────────────────┐
│  状态栏 [9:41 AM] 📶 🔋 78%              │
├─────────────────────────────────────────┤
│  🚨 紧急警报                             │
│                                         │
│  ┌─────────────────────────────────────┐│
│  │ ⚠️  旺财已离开安全区域               ││
│  │ 📍 最后位置: 朝阳区三里屯            ││
│  │ ⏰ 5分钟前                           ││
│  │                                     ││
│  │ [立即导航] [一键报警]               ││
│  └─────────────────────────────────────┘│
│                                         │
│  ┌─────────────────────────────────────┐│
│  │ [旺财头像] ⚠️ 离线                   ││
│  │ 电量: 15% 🔴 低电量警报              ││
│  └─────────────────────────────────────┘│
└─────────────────────────────────────────┘
```

### 3.3 地图界面完整交互

#### 3.3.1 实时地图 - 多状态展示
```
┌─────────────────────────────────────────┐
│  [返回] 实时追踪   [图层] [更多]          │
├─────────────────────────────────────────┤
│                                         │
│  [地图区域 - 高德地图嵌入]                │
│                                         │
│  [旺财头像标记]                          │
│    ↓ 实时移动动画                        │
│                                         │
│  ┌─────────────────────────────────────┐│
│  │  旺财位置信息                        ││
│  │  📍 朝阳区三里屯太古里               ││
│  │  🏃 正在移动  速度: 2.3km/h         ││
│  │  🔋 78%  预计可用5天                 ││
│  │                                     ││
│  │ [导航到宠物] [共享位置] [语音通话]     ││
│  └─────────────────────────────────────┘│
│                                         │
│  [轨迹回放按钮] [电子围栏开关]            │
└─────────────────────────────────────────┘
```

#### 3.3.2 轨迹回放界面
```
┌─────────────────────────────────────────┐
│  [返回] 轨迹回放   [时间选择] [分享]       │
├─────────────────────────────────────────┤
│  [地图区域]                              │
│  [轨迹线 - 从家到公园]                   │
│  [时间节点标记]                          │
│                                         │
│  ┌─────────────────────────────────────┐│
│  │ 轨迹详情                             ││
│  │ 总距离: 3.2公里                      ││
│  │ 总时长: 45分钟                       ││
│  │ 平均速度: 4.3km/h                    ││
│  │ 停留点: 公园(15分钟)                 ││
│  │                                     ││
│  │ [播放轨迹] [倍速选择] [导出GPX]        ││
│  └─────────────────────────────────────┘│
└─────────────────────────────────────────┘
```

### 3.4 AI对话界面完整体验

#### 3.4.1 对话主界面
```
┌─────────────────────────────────────────┐
│  [返回] 与旺财对话   [设置] [历史记录]     │
├─────────────────────────────────────────┤
│                                         │
│  ┌─────────────────────────────────────┐│
│  │ 旺财当前情绪: 😊 开心                ││
│  │ 活跃状态: 在公园玩耍                 ││
│  └─────────────────────────────────────┘│
│                                         │
│  [对话气泡区域 - 可滚动]                  │
│                                         │
│  ┌─────────────────────────────────────┐│
│  │ 你: 旺财你在做什么？                ││
│  └─────────────────────────────────────┘│
│                                         │
│  ┌─────────────────────────────────────┐│
│  │ 旺财: 我在公园追蝴蝶，好开心！       ││
│  │      这里有很多小伙伴在玩耍        ││
│  └─────────────────────────────────────┘│
│                                         │
│  ┌─────────────────────────────────────┐│
│  │ [语音输入按钮] [文字输入] [表情]      ││
│  └─────────────────────────────────────┘│
└─────────────────────────────────────────┘
```

#### 3.4.2 语音对话界面
```
┌─────────────────────────────────────────┐
│  [返回] 语音通话   [静音] [扬声器]        │
├─────────────────────────────────────────┤
│                                         │
│     [旺财头像 - 脉冲动画]                │
│         [语音波形可视化]                 │
│                                         │
│  ┌─────────────────────────────────────┐│
│  │ 正在通话...                          ││
│  │ 时长: 02:34                          ││
│  │ 音量: ████████░░ 80%               ││
│  └─────────────────────────────────────┘│
│                                         │
│  [按住说话] ●──────● [挂断]             │
│                                         │
│  [快捷短语: "快回家" "坐下" "真乖"]      │
└─────────────────────────────────────────┘
```

---

## 4. 完整交互原型定义

### 4.1 手势交互系统

#### 4.1.1 标准手势库
| 手势类型 | 触发区域 | 动画效果 | 响应时间 |
|----------|----------|----------|----------|
| **点击** | 所有按钮 | 缩放0.95 → 1.0 | 150ms |
| **长按** | 宠物卡片 | 菜单弹出动画 | 500ms |
| **滑动** | 卡片列表 | 弹性滑动 | 跟随手指 |
| **下拉** | 列表顶部 | 弹性刷新回弹 | 300ms |
| **双指缩放** | 地图区域 | 平滑缩放 | 跟随手势 |
| **3D Touch** | 主按钮 | 快捷菜单 | 立即响应 |

#### 4.1.2 高级交互示例
```typescript
// 宠物卡片滑动交互
interface CardSwipeGesture {
  threshold: 50px;        // 滑动阈值
  velocity: 0.3;          // 速度阈值
  animation: {
    duration: 300ms;
    easing: 'spring';
    overshoot: 0.2;
  };
  feedback: {
    haptic: 'light';
    sound: 'click';
  };
}
```

### 4.2 动效设计系统

#### 4.2.1 微交互动效库
```css
/* 按钮状态过渡 */
.button {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.button:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 位置标记动画 */
.location-marker {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.1); opacity: 0.8; }
  100% { transform: scale(1); opacity: 1; }
}
```

#### 4.2.2 页面转场动效
```typescript
// 页面切换动画配置
const pageTransitions = {
  slide: {
    duration: 300,
    easing: 'easeInOut',
    direction: 'horizontal'
  },
  fade: {
    duration: 200,
    easing: 'easeOut'
  },
  modal: {
    duration: 250,
    easing: 'spring',
    backdrop: true
  }
};
```

---

## 5. 响应式原型设计

### 5.1 断点设计系统

#### 5.1.1 设备适配矩阵
| 设备类型 | 尺寸范围 | 布局模式 | 组件调整 |
|----------|----------|----------|----------|
| **iPhone SE** | 375×667 | 单列紧凑 | 按钮48px |
| **iPhone 12/13** | 390×844 | 标准单列 | 按钮48px |
| **iPhone 14 Pro** | 393×852 | 标准单列 | 按钮48px |
| **iPad Mini** | 768×1024 | 双列布局 | 按钮44px |
| **iPad Pro** | 1024×1366 | 三列布局 | 按钮52px |

#### 5.1.2 响应式组件示例
```typescript
// 宠物卡片响应式配置
const cardResponsive = {
  mobile: {
    width: '100%',
    padding: 16,
    avatar: 48,
    fontSize: 16
  },
  tablet: {
    width: 'calc(50% - 8px)',
    padding: 20,
    avatar: 56,
    fontSize: 18
  },
  desktop: {
    width: 'calc(33.33% - 10.67px)',
    padding: 24,
    avatar: 64,
    fontSize: 20
  }
};
```

### 5.2 无障碍设计原型

#### 5.2.1 无障碍交互测试
```
高保真原型包含的无障碍功能:

✅ 语音控制: "打开旺财位置"
✅ 大字体模式: 200%字体缩放
✅ 高对比度: 黑白模式切换
✅ 单手操作: 重要按钮下移
✅ 语音反馈: 状态朗读功能
✅ 震动反馈: 关键操作确认
```

---

## 6. 原型测试规范

### 6.1 可用性测试场景

#### 6.1.1 核心任务测试
| 任务编号 | 任务描述 | 成功标准 | 预期时间 |
|----------|----------|----------|----------|
| **T-01** | 首次配对设备 | 5分钟内完成 | 3分30秒 |
| **T-02** | 设置电子围栏 | 3分钟内完成 | 2分15秒 |
| **T-03** | 紧急寻宠操作 | 30秒内启动导航 | 15秒 |
| **T-04** | 查看历史轨迹 | 2分钟内找到轨迹 | 45秒 |
| **T-05** | 与宠物AI对话 | 完成3轮对话 | 2分钟 |

#### 6.1.2 用户测试脚本
```typescript
// 高保真测试场景
const testScenarios = [
  {
    name: "新手首次使用",
    steps: [
      "打开APP",
      "完成设备配对",
      "设置家为安全区域",
      "测试语音通话",
      "查看宠物当前位置"
    ],
    metrics: ["任务完成率", "错误次数", "满意度"]
  }
];
```

### 6.2 性能测试原型

#### 6.2.1 加载性能模拟
```typescript
// 真实网络条件模拟
const networkConditions = {
  wifi: { download: 50, upload: 20, latency: 20 },
  lte: { download: 20, upload: 5, latency: 50 },
  _3g: { download: 2, upload: 1, latency: 200 },
  offline: { download: 0, upload: 0, latency: 0 }
};

// 加载状态展示
const loadingStates = {
  skeleton: "骨架屏占位",
  progressive: "渐进式加载",
  cached: "缓存优先加载",
  offline: "离线模式提示"
};
```

---

## 7. 原型交付清单

### 7.1 完整原型文件结构

```
aichong-hifi-prototype/
├── 01-设计系统/
│   ├── 色彩系统.fig
│   ├── 字体系统.fig
│   ├── 图标库.fig
│   └── 组件库.fig
├── 02-页面原型/
│   ├── 01-启动引导/
│   │   ├── 欢迎页.fig
│   │   ├── 权限请求.fig
│   │   ├── 设备配对.fig
│   │   └── 引导完成.fig
│   ├── 02-主界面/
│   │   ├── 首页-正常.fig
│   │   ├── 首页-紧急.fig
│   │   └── 首页-离线.fig
│   ├── 03-地图功能/
│   │   ├── 实时地图.fig
│   │   ├── 轨迹回放.fig
│   │   └── 电子围栏.fig
│   ├── 04-设备管理/
│   │   ├── 设备详情.fig
│   │   ├── 固件升级.fig
│   │   └── 故障诊断.fig
│   ├── 05-AI对话/
│   │   ├── 对话主界面.fig
│   │   ├── 语音通话.fig
│   │   └── 情绪识别.fig
│   └── 06-设置与帮助/
│       ├── 个人设置.fig
│       ├── 设备设置.fig
│       └── 帮助中心.fig
├── 03-交互原型/
│   ├── 用户流程演示.fig
│   ├── 手势操作演示.fig
│   └── 动效展示.fig
└── 04-测试文件/
    ├── 可用性测试脚本.pdf
    ├── 用户测试任务单.pdf
    └── 反馈收集模板.xlsx
```

### 7.2 原型技术规格表

| 项目 | 规格值 | 备注 |
|------|--------|------|
| **设计工具** | Figma Professional | 团队协作版 |
| **原型工具** | Figma Prototype + ProtoPie | 高级交互 |
| **分辨率** | 375×812 @2x | iPhone 12标准 |
| **色彩模式** | sRGB | 标准色域 |
| **文件大小** | <100MB | 优化压缩 |
| **交互层级** | 5层深度 | 完整用户流程 |
| **动态组件** | 50+ | 真实状态切换 |
| **响应式** | 3个断点 | 手机/平板/桌面 |

---

## 8. 原型测试与验证

### 8.1 测试环境配置

#### 8.1.1 真实设备测试
```
测试设备清单:
- iPhone 12 Pro (iOS 17.4)
- iPhone 14 Pro Max (iOS 17.5)
- Samsung Galaxy S23 (Android 14)
- iPad Air (iPadOS 17.4)
- MacBook Pro (macOS 14.4)

测试场景:
- WiFi环境: 50Mbps下载/20Mbps上传
- 4G网络: 20Mbps下载/5Mbps上传
- 弱网环境: 2G网络模拟
- 离线环境: 无网络状态
```

#### 8.1.2 无障碍测试检查表
```
视觉无障碍:
□ 颜色对比度 WCAG 2.1 AA标准
□ 支持系统大字体(200%缩放)
□ 高对比度模式适配
□ 色盲友好设计

操作无障碍:
□ 单手操作优化
□ 语音控制支持
□ 触摸目标≥44×44像素
□ 震动反馈确认
```

### 8.2 原型迭代计划

#### 8.2.1 第一阶段测试 (1周)
- **测试用户**: 5名目标用户
- **测试内容**: 核心功能流程
- **反馈收集**: 可用性问题、视觉建议
- **迭代重点**: 修复明显可用性问题

#### 8.2.2 第二阶段测试 (1周)
- **测试用户**: 10名目标用户
- **测试内容**: 完整功能测试
- **深度访谈**: 30分钟用户访谈
- **最终优化**: 细节完善

---

## 9. 开发交接规范

### 9.1 设计标注系统

#### 9.1.1 Figma Inspect配置
```
自动标注内容:
- 所有尺寸标注 (px/pt/dp)
- 颜色十六进制值
- 字体规格 (家族/大小/字重)
- 阴影效果参数
- 圆角数值
- 间距8点网格对齐

导出格式:
- PNG @2x @3x
- SVG (图标和插画)
- PDF (完整页面)
- Lottie JSON (动效)
```

#### 9.2 动效交付规范

#### 9.2.1 动效参数表
```typescript
// 标准动效配置
const standardTransitions = {
  duration: {
    micro: 150,   // 微交互
    minor: 200,   // 状态切换
    major: 300,   // 页面转场
    macro: 400    // 复杂动画
  },
  easing: {
    standard: 'cubic-bezier(0.4, 0, 0.2, 1)',
    decelerate: 'cubic-bezier(0, 0, 0.2, 1)',
    accelerate: 'cubic-bezier(0.4, 0, 1, 1)'
  }
};
```

---

## 10. 原型演示指南

### 10.1 用户演示脚本

#### 10.1.1 5分钟完整演示
```
开场 (30秒):
"欢迎来到爱宠记APP演示，这是一款AI智能宠物守护应用。"

核心功能展示 (3分钟):
1. 实时定位演示 (45秒)
2. 紧急寻宠流程 (45秒)  
3. AI对话体验 (60秒)
4. 设备管理操作 (30秒)

Q&A环节 (90秒):
- 功能疑问解答
- 使用场景讨论
- 改进建议收集
```

### 10.2 原型文件交付

#### 10.2.1 最终交付包
```
aichong-hifi-deliverable.zip
├── 📁 设计文件/
│   ├── aichong-app-hifi.fig (主文件)
│   ├── aichong-components.fig (组件库)
│   └── aichong-interactions.fig (交互原型)
├── 📁 标注文件/
│   ├── design-specs.pdf
│   ├── color-palette.pdf
│   └── typography-guide.pdf
├── 📁 测试文件/
│   ├── usability-test-script.pdf
│   ├── user-feedback-template.xlsx
│   └── prototype-links.txt
└── 📁 资源文件/
    ├── icons-svg/
    ├── illustrations/
    └── mockup-templates/
```