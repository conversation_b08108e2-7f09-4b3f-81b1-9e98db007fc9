# 爱宠记APP UI/UX设计规范
**设计负责人**: Alex  
**版本**: v1.0  
**创建日期**: 2025-08-14  

---

## 1. 设计哲学与核心原则

### 1.1 情感化设计理念
**核心理念**: "科技让陪伴更有温度"
- **安全感**: 通过视觉设计传递可靠的安全守护
- **亲切感**: 温暖治愈的配色和圆润的造型语言
- **科技感**: 精确的数据可视化和流畅的交互动效
- **人性化**: 以宠物主人情感需求为中心的设计

### 1.2 设计原则
| 原则 | 具体体现 | 应用示例 |
|------|----------|----------|
| **清晰易懂** | 信息层级分明，操作流程简化 | 一键寻宠按钮位于显著位置 |
| **情感连接** | 宠物头像、昵称个性化展示 | 首页展示宠物实时照片和状态 |
| **即时反馈** | 所有操作都有明确的状态反馈 | 定位时显示进度环和预计时间 |
| **无障碍设计** | 支持语音操作、大字体模式 | 老年用户友好的简化界面 |

---

## 2. 用户画像与场景分析

### 2.1 核心用户画像

#### 画像A: 都市白领宠物妈妈
- **年龄**: 28-35岁
- **特征**: 高学历、高收入、将宠物视为孩子
- **痛点**: 工作日担心宠物安全，需要远程监控
- **使用场景**: 上班时间查看宠物状态，设置电子围栏

#### 画像B: 退休宠物爷爷奶奶
- **年龄**: 55-70岁
- **特征**: 时间充裕，技术接受度中等
- **痛点**: 担心宠物走失，需要简单易用的功能
- **使用场景**: 遛宠时设置安全区域，紧急寻宠

#### 画像C: 年轻宠物爱好者
- **年龄**: 20-28岁
- **特征**: 科技敏感，喜欢尝鲜
- **痛点**: 希望与宠物有更多互动方式
- **使用场景**: 体验AI对话功能，分享宠物日常

### 2.2 关键使用场景

#### 场景1: 日常监控模式
```
早晨出门前 → 检查宠物位置 → 设置工作时段围栏 → 查看今日活动计划
```

#### 场景2: 紧急寻宠模式
```
发现宠物走失 → 一键报警 → 实时追踪 → 导航寻宠 → 社区求助
```

#### 场景3: 情感互动模式
```
想念宠物 → AI对话 → 语音安抚 → 远程拍照 → 分享朋友圈
```

---

## 3. 信息架构设计

### 3.1 功能架构图
```
爱宠记APP
├── 首页监控 (Home)
│   ├── 实时位置监控
│   ├── 宠物状态卡片
│   └── 快捷操作区
├── 定位追踪 (Track)
│   ├── 实时地图
│   ├── 轨迹回放
│   └── 导航寻宠
├── 设备控制 (Control)
│   ├── 电子围栏设置
│   ├── 语音通话
│   └── 设备管理
├── AI互动 (AI)
│   ├── 人宠对话
│   ├── 情绪识别
│   └── 行为分析
└── 个人中心 (Profile)
    ├── 宠物档案
    ├── 设置偏好
    └── 帮助支持
```

### 3.2 用户流程设计

#### 3.2.1 新用户引导流程
```
欢迎页 → 设备配对 → 宠物档案创建 → 基础设置 → 功能教学 → 进入主界面
(30秒)   (1分钟)    (2分钟)        (1分钟)    (1分钟)      (总计5分钟)
```

#### 3.2.2 紧急寻宠流程
```
发现走失 → 紧急按钮 → 确认宠物 → 启动全速追踪 → 导航到宠物位置
(3秒)    (2秒)     (5秒)      (自动)         (实时更新)
```

---

## 4. 视觉设计系统

### 4.1 品牌色彩系统

#### 4.1.1 主色调
- **品牌主色**: 温暖橙 (#FF6B35)
  - 象征: 温暖、活力、安全
  - 应用: 主按钮、重要状态、品牌标识
  
- **辅助色**: 治愈蓝 (#4ECDC4)
  - 象征: 科技、信任、宁静
  - 应用: 背景、次要信息、成功状态

- **强调色**: 阳光黄 (#FFE66D)
  - 象征: 希望、快乐、警示
  - 应用: 提醒、警告、高亮信息

#### 4.1.2 中性色
```
灰色系:
- 文字主色: #2C3E50 (深灰蓝)
- 文字次要: #7F8C8D (中灰色)
- 分割线: #ECF0F1 (浅灰色)
- 背景色: #FFFFFF (纯白)
- 卡片背景: #F8F9FA (极浅灰)
```

### 4.2 字体系统

#### 4.2.1 字体层级
| 层级 | 字体大小 | 字重 | 应用场景 | 行高 |
|------|----------|------|----------|------|
| **H1** | 28px | Bold | 主标题、宠物名字 | 1.2 |
| **H2** | 22px | SemiBold | 页面标题、重要信息 | 1.3 |
| **H3** | 18px | Medium | 模块标题、分类标签 | 1.4 |
| **Body** | 16px | Regular | 正文内容、描述文字 | 1.5 |
| **Caption** | 14px | Regular | 辅助信息、时间戳 | 1.4 |
| **Small** | 12px | Regular | 标签、状态文字 | 1.3 |

#### 4.2.2 字体选择
- **中文**: 思源黑体 / 苹方
- **英文**: SF Pro / Roboto
- **数字**: DIN Condensed (用于距离、时间等数值显示)

### 4.3 图标系统

#### 4.3.1 图标风格
- **线性图标**: 简洁现代，用于功能性操作
- **面性图标**: 丰富饱满，用于状态展示
- **双色图标**: 品牌色彩组合，用于突出重点

#### 4.3.2 图标规范
```
图标网格: 24×24px
描边宽度: 2px
圆角半径: 2px
断口大小: 2px
```

#### 4.3.3 核心图标库
| 功能 | 图标设计 | 说明 |
|------|----------|------|
| **定位** | 📍 渐变橙色定位针 | 实时位置状态 |
| **导航** | 🧭 动态指南针 | 寻宠导航 |
| **语音** | 🎤 脉冲音波效果 | 通话状态 |
| **围栏** | 🛡️ 动态边界线 | 电子围栏状态 |
| **AI对话** | 💬 气泡+AI标识 | 智能对话入口 |

### 4.4 插画与动效

#### 4.4.1 插画风格
- **手绘风格**: 温暖治愈的宠物插画
- **几何简化**: 抽象化的宠物形象
- **情感表达**: 通过表情和动作传递情绪

#### 4.4.2 动效设计原则
- **功能性动效**: 提升操作反馈和状态理解
- **情感化动效**: 增强用户与宠物的情感连接
- **性能优化**: 确保流畅度，不影响核心功能

---

## 5. 界面设计规范

### 5.1 首页设计 (Home Dashboard)

#### 5.1.1 布局结构
```
┌─────────────────────────────────┐
│  状态栏 (时间、电量、信号)         │
├─────────────────────────────────┤
│  宠物信息卡片                      │
│  ┌─────┐ 旺财 · 2岁 · 金毛          │
│  │头像 │ ● 在线  电量78% 🔋        │
│  └─────┘ 今日已走5230步 🚶        │
├─────────────────────────────────┤
│  核心功能区 (2×3网格)               │
│  ┌─────┐ ┌─────┐ ┌─────┐         │
│  │实时  │ │导航  │ │语音  │         │
│  │定位  │ │寻宠  │ │通话  │         │
│  └─────┘ └─────┘ └─────┘         │
│  ┌─────┐ ┌─────┐ ┌─────┐         │
│  │电子  │ │轨迹  │ │AI    │         │
│  │围栏  │ │回放  │ │对话  │         │
│  └─────┘ └─────┘ └─────┘         │
├─────────────────────────────────┤
│  快速状态栏                        │
│  📍 在家附近  🏠 安全区域          │
│  ⏰ 上次更新 2分钟前               │
└─────────────────────────────────┘
```

#### 5.1.2 交互细节
- **下拉刷新**: 更新宠物实时位置
- **卡片滑动**: 左右滑动切换多只宠物
- **长按操作**: 长按宠物头像进入详情页
- **3D Touch**: 重按快捷按钮快速操作

### 5.2 地图界面 (Map View)

#### 5.2.1 地图样式定制
- **宠物专属地图**: 淡化的地图底图，突出宠物位置
- **动态标记**: 宠物头像实时移动，显示运动轨迹
- **热力图**: 宠物活动频繁区域用暖色显示
- **区域着色**: 电子围栏用半透明橙色标示

#### 5.2.2 信息窗口设计
```
┌─────────────────────────┐
│  [宠物头像] 旺财         │
│  🐕 金毛 · 2岁          │
│  📍 距离您 156米        │
│  🔋 电量 78%            │
│  ⏰ 停留 15分钟          │
│  ┌─────────┐ ┌─────────┐│
│  │  导航   │ │  语音   ││
│  └─────────┘ └─────────┘│
└─────────────────────────┘
```

### 5.3 设备控制界面

#### 5.3.1 电子围栏设置
```
┌─────────────────────────┐
│  设置电子围栏             │
│                         │
│  [地图预览]              │
│  ┌─────────────────────┐│
│  │                     ││
│  │   [圆形区域]        ││
│  │                     ││
│  └─────────────────────┘│
│                         │
│  围栏范围                │
│  ○ 100米  ○ 200米  ● 500米│
│                         │
│  触发方式                │
│  ☑ 进入提醒  ☑ 离开提醒   │
│                         │
│  [保存设置]              │
└─────────────────────────┘
```

#### 5.3.2 语音通话界面
```
┌─────────────────────────┐
│  正在与旺财通话           │
│                         │
│    [宠物头像动画]         │
│                         │
│  ┌─────────────────────┐│
│  │  "旺财，快回家吃饭啦"  ││
│  └─────────────────────┘│
│                         │
│  🔊 [按住说话] 🔇 [静音]  │
│  📞 [挂断] 🔄 [重播]      │
└─────────────────────────┘
```

### 5.4 AI对话界面

#### 5.4.1 对话气泡设计
- **用户消息**: 右侧蓝色气泡，白色文字
- **AI消息**: 左侧灰色气泡，深色文字
- **宠物状态**: 中间橙色小标签显示当前情绪
- **输入框**: 底部固定，支持语音转文字

#### 5.4.2 情绪可视化
```
旺财现在的心情:
😊 开心 (85%)
😟 有点想主人 (60%)
😴 有点困了 (40%)
```

---

## 6. 交互设计规范

### 6.1 手势操作规范

| 手势 | 功能区域 | 操作结果 |
|------|----------|----------|
| **单击** | 宠物卡片 | 进入宠物详情 |
| **双击** | 地图宠物标记 | 快速导航 |
| **长按** | 功能按钮 | 显示操作菜单 |
| **左滑** | 宠物卡片 | 切换到下一宠物 |
| **右滑** | 宠物卡片 | 切换到上一宠物 |
| **上滑** | 地图底部 | 展开详细信息 |
| **下拉** | 任意页面 | 刷新数据 |
| **双指缩放** | 地图界面 | 缩放地图视图 |
| **摇一摇** | 主界面 | 触发紧急寻宠 |

### 6.2 反馈机制

#### 6.2.1 状态反馈
- **成功**: 绿色对勾 + 微动画
- **警告**: 橙色感叹号 + 温和提醒
- **错误**: 红色叉号 + 解决建议
- **加载**: 宠物爪印旋转动画

#### 6.2.2 声音反馈
- **成功提示**: 清脆的"叮"声
- **警告提示**: 温和的提示音
- **紧急警报**: 连续的急促提示音
- **AI对话**: 宠物友好的提示音

### 6.3 无障碍设计

#### 6.3.1 视觉无障碍
- **字体大小**: 支持大字体模式(1.5x、2x)
- **颜色对比**: 符合WCAG 2.1 AA标准
- **图标描述**: 所有图标都有文字标签
- **高对比模式**: 黑白高对比度选项

#### 6.3.2 操作无障碍
- **语音控制**: 支持语音导航所有功能
- **单手操作**: 重要功能都在拇指可触区域
- **简化模式**: 老年用户专属简化界面
- **震动反馈**: 重要状态变化提供震动提示

---

## 7. 响应式设计

### 7.1 断点设计
| 设备类型 | 断点宽度 | 布局调整 |
|----------|----------|----------|
| **手机** | 375-768px | 单列布局，底部导航 |
| **平板** | 768-1024px | 双列布局，侧边导航 |
| **桌面** | 1024px+ | 三列布局，顶部导航 |

### 7.2 适配策略

#### 7.2.1 手机端优化
- **底部导航**: 5个核心功能入口
- **单手操作**: 重要按钮位于下半屏
- **滑动操作**: 充分利用滑动手势
- **省电模式**: 简化动画，降低功耗

#### 7.2.2 平板端优化
- **分屏显示**: 地图+控制面板并排
- **多宠物管理**: 同时显示多只宠物状态
- **增强交互**: 支持拖拽设置围栏
- **键盘支持**: 外接键盘快捷操作

---

## 8. 设计组件库

### 8.1 基础组件

#### 8.1.1 按钮规范
```
主按钮: 高度48px，圆角24px，填充品牌色
次按钮: 高度44px，圆角22px，描边样式
文字按钮: 高度36px，无背景，下划线悬停
危险按钮: 红色填充，用于删除等危险操作
```

#### 8.1.2 输入框规范
```
标准输入框: 高度48px，内边距16px
圆角: 8px，边框: 1px #E0E0E0
聚焦状态: 边框变为品牌色，有阴影
错误状态: 边框红色，下方显示错误提示
```

#### 8.1.3 卡片设计
```
标准卡片: 圆角12px，阴影0 2px 8px rgba(0,0,0,0.1)
悬停效果: 阴影加深，微动画上移2px
点击效果: 按压效果，阴影变浅
```

### 8.2 业务组件

#### 8.2.1 宠物状态卡片
```
┌─────────────────────────┐
│  [头像] 旺财            │
│  🐕 金毛 · 2岁          │
│  📍 在家附近 156米       │
│  🔋 78%  预计可用5天     │
│  [实时位置] [语音通话]     │
└─────────────────────────┘
```

#### 8.2.2 位置标记组件
```
┌─────────┐
│ [头像]  │
│ 旺财    │
│ 78%🔋   │
└─────────┘
```

#### 8.2.3 围栏设置组件
```
┌─────────────────────────┐
│  🛡️ 电子围栏             │
│  ┌─────────────────────┐│
│  │  [地图预览]         ││
│  └─────────────────────┘│
│  范围: 200米            │
│  状态: 已激活            │
│  [修改设置] [临时关闭]     │
└─────────────────────────┘
```

---

## 9. 动效设计规范

### 9.1 微交互动效

#### 9.1.1 加载动效
- **定位加载**: 雷达扫描效果，2秒循环
- **数据同步**: 旋转的宠物爪印，1.5秒
- **AI思考**: 脉冲呼吸效果，表示正在处理

#### 9.1.2 状态切换动效
- **围栏开启/关闭**: 平滑的开关动画
- **设备连接/断开**: 心跳连线效果
- **电量变化**: 平滑的数字增减动画

### 9.2 情感化动效

#### 9.2.1 宠物情绪表达
- **开心**: 尾巴摇摆动画
- **焦虑**: 左右踱步动画
- **睡觉**: 缓慢的呼吸起伏

#### 9.2.2 特殊场景动效
- **找回成功**: 庆祝彩带动画
- **宠物走失**: 紧急闪烁警示
- **新功能引导**: 友好的手势提示

---

## 10. 设计工具与交付规范

### 10.1 设计工具
- **Figma**: 主要设计工具，支持实时协作
- **Principle**: 高保真动效原型
- **Zeplin**: 设计交付与标注
- **Sketch**: 图标与插画设计

### 10.2 文件命名规范
```
项目/页面/状态/组件
例如: Home/Dashboard/Default/PetCard
```

### 10.3 切图规范
- **格式**: SVG优先，PNG备用
- **尺寸**: @2x, @3x倍图
- **命名**: icon_功能_状态.png
- **压缩**: TinyPNG优化，保持质量

### 10.4 设计交付清单
- [ ] 完整设计稿 (Figma文件)
- [ ] 交互原型 (Principle/ProtoPie)
- [ ] 设计规范文档
- [ ] 图标库 (SVG格式)
- [ ] 切图资源包
- [ ] 动效说明文档
- [ ] 无障碍设计说明

---

## 11. 测试与验证

### 11.1 可用性测试计划
- **测试用户**: 20名目标用户
- **测试场景**: 5个核心任务
- **测试指标**: 任务完成率、错误率、满意度
- **测试方法**: 观察法、访谈法、问卷法

### 11.2 A/B测试方案
| 测试项 | 版本A | 版本B | 目标指标 |
|--------|--------|--------|----------|
| **主按钮样式** | 填充橙色 | 描边橙色 | 点击率 |
| **地图标记** | 头像样式 | 图标样式 | 识别度 |
| **紧急按钮** | 顶部固定 | 底部浮动 | 响应时间 |

### 11.3 性能测试指标
- **加载时间**: 首屏<2秒，交互<0.5秒
- **流畅度**: 滚动帧率>50fps
- **内存占用**: <100MB
- **电量消耗**: 后台运行<5%/小时