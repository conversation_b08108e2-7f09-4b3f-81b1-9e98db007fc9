# **项目提示词**

- # 需求分析 PRD提示词
- # Q:

@team-ide-minimal.txt
Sarah，我现在要完成爱宠记APP的开发。请帮我完成需求分析，并完成PRD编写，详细的功能需求如下：
```md
# 爱宠记APP - 宠物项圈功能定义

## 精准定位
- 多重定位模式覆盖更多场景
- 北斗 GPS LBS基站定位WIFI定位

## 一键导航寻宠
- 点击导航按钮，即可生成路线图，方便寻宠

## 通话唤宠
- 手机可拨打电话给设备，设备自动接听，可随时呼唤爱宠回家，十分便携

## 响铃寻宠
- APP发起查找设备，设备会持续响铃，寻宠更方便

## 扫码寻主
- 扫描设备上的二维码可以联系到主人，增加宠物丢失找回概率

## 电子围栏
- 超出设置范围自动报警
- 有效防止宠物和设备丢失

## 轨迹寻宠
- 随时查看宠物轨迹

## 磁吸防拆
- 采用防拆钉固定，需要使用特定的解锁口拆卸，蛮力不可拆除

## 人宠对话
- 接入AI大模型，实现人宠对话
```
- # A:

- # UI UX设计提示词
- # Q: 
Alex，基于Sarah的PRD，创建一套完整的UI/UX设计规范
- # A:

- # 架构设计提示词
- # Q:
David，根据prd请帮我完成架构设计，优先考虑跨平台开发框架，需要包含完整全栈架构，API定义，数据库设计等方面
- # A:

- # 高保真原型开发提示词
- # Q:
Alex，完成完整的高保真原型的设计规范
- # A:

- # 原型设计风格确认提示词
- # Q:
Alex，根据设计语言分析确认APP的风格


| 设计语言类别          | 关键特征                  | 代表 App（2025）                           |
| --------------- | --------------------- | -------------------------------------- |
| Bento Box 栅格    | 分区卡片、非对称栅格、留白呼吸感      | 小红书（新版首页）、知乎直答、即刻、Notion 中国版           |
| 功能性微动效          | 60-120 ms 轻动画、状态反馈    | 微信、支付宝、抖音、B 站、高德地图                     |
| 玻璃/新拟态 2.0      | 半透明 + 柔和投影、圆润 Clay 质感 | 抖音极速版、剪映、飞书、Linear 中国官网                |
| 沉浸式 AR/VR       | 空间界面、HDR、虚实叠影         | 淘宝 Vision 版、QQ「超级 QQ」、抖音 VR、高德 AR 步行导航 |
| AI 深度个性化        | 零点击首页、动态主题、实时预测       | Spotify 中国版、网易云音乐、百度 App、夸克浏览器         |
| 暖色低对比暗色         | 深夜护眼、柔和光晕、情绪安抚        | 微信「深夜模式」、QQ 音乐、知乎、Keep                 |
| 可访问性优先          | WCAG 2.2、大字号、高对比、语音朗读 | 支付宝长辈版、铁路 12306 关怀版、中国移动和我             |
| 中国风 Neo-Chinese | 水墨、留白、传统纹样与现代极简融合     | 得到「国风主题」、支付宝「国潮集五福」、QQ「中国色」主题          |
| 3D/超现实          | 3D 场景、空间导航、手势交互       | 和平营地、QQ 秀 3D、淘宝「3D 逛街」、王者荣耀赛事 Tab      |
| 极简主义            | 大留白、单色、无分隔线           | 飞书文档、石墨文档、纯纯写作、小宇宙                     |
- # A:

- # 原型开发提示词
- # Q:
James，请开始高保真原型设计：
1、使用HTML5、Tailwind css 4.0、**iconify-icons**、chart.js、Alpine.js和JavaScript，通过cdn`unpkg.com`引入，
2、每个页面一个文件，请正确的引入cnd图标库，不可以使用emoji作为icon;
3、使用ios设计规范，真实地模拟App体验，页面无需边框和顶栏，页面宽高能自适应;
4、完整阅读docs/*下文档，特别重要文档定义在{prd|*ui/x*|*design*}.md中;
5、以功能性微动效 + AI个性化的设计语言，玻璃/新拟态2.0（圆润 Clay 质感）组件 + 暖色低对比暗色主题为核心概念;
6、品牌视觉等全局样式使用css变量定义在assets/css/styles.css下，所有页面统一引入
7、使用`classMode`模式进行暗色模式适配，将taiwindcss配置定义在assets/js/*下，所有页面统一引入;
8、实现全局的主题管理prototype/assets/js/theme.js实现主题{亮|暗}模式切换，所有页面统一引入;
9、在prototype/pages/*目录下，完成高保真原型的编码
10、请先制详细的开发计划，再按计划一步一步执行，最后维护更新prototype\{PROJECT_SUMMARY|DEVELOPMENT_PROGRESS|README}.md文档
- # A:

- # 更新软著文档提示词
- # Q:
James，请识别项目文档docs/*和高保真原型页面prototype/*下的文件，请顺序执行任务:
1. 对prototype/{preview.html|COPYRIGHT.txt|INSTRUCTIONS.md}非本项目信息进行删减替换，不要进行破坏性的修改和删除，确保文档整体结构的完整性
2. 更新prototype/preview.html的css变量为本项目数据，alpinejs数据为prototype/pages/*下所有页面
3. 更新prototype/INSTRUCTIONS.md的软件功能为prototype/pages/*下所有页面
4. 先进行思路的设计，再按步骤执行
- # A:

- # 更新文档
- # Q:
James，请识别项目文档docs/*和高保真原型页面prototype/*下的文件，请顺序执行任务:
1. 识别prototype/pages/*所有页面，对prototype/README.md进行更新
2. 先进行思路的设计，再按步骤执行
- # A: